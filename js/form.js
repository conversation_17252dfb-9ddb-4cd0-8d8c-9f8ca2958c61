document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('contact-form');
  
    // Referencias a campos
    const nombre   = document.getElementById('nombre');
    const apellido = document.getElementById('apellido');
    const correo   = document.getElementById('correo');
    const telefono = document.getElementById('telefono');
    const empresa  = document.getElementById('empresa');
    const cargo    = document.getElementById('cargo');
    const mensaje  = document.getElementById('mensaje');
  
    // Para reCAPTCHA, no hay un input "HTML" que podamos escuchar.
    // Validamos en submit (o cuando quieras).
    const captchaError = document.getElementById('error-captcha');
  
    // Campos a validar individualmente
    const campos = [nombre, apellido, correo, telefono, empresa, cargo, mensaje];
  
    // Validar en tiempo real
    campos.forEach(campo => {
      campo.addEventListener('input', () => {
        validarCampo(campo);
      });
    });
  
    // Validación final en el "submit"
    form.addEventListener('submit', (e) => {
      let hayErrores = false;
      // Validamos cada campo
      campos.forEach(campo => {
        const errorCampo = validarCampo(campo);
        if (errorCampo) hayErrores = true;
      });
  
      // Validar el reCAPTCHA
      const captchaValido = validarCaptcha();
      if (!captchaValido) {
        hayErrores = true;
      }
  
      if (hayErrores) {
        e.preventDefault(); // Evitamos el envío
      }
    });
  
    /**
     * Valida un campo de acuerdo a su id y sus reglas.
     * Si hay error, muestra mensaje y retorna true (hay error).
     * Si no hay error, retorna false.
     */
    function validarCampo(campo) {
      const valor = campo.value.trim();
      const errorSpan = document.getElementById(`error-${campo.id}`);
      let mensajeError = '';
  
      // 1. Borrar estados previos
      campo.classList.remove('error-input');
      errorSpan.textContent = '';
      errorSpan.style.display = 'none';
  
      // 2. Aplicar reglas según el campo
      switch (campo.id) {
        case 'nombre':
        case 'apellido':
          // Solo letras, espacios y acentos/ñ. Mínimo 2 caracteres.
          if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,}$/.test(valor)) {
            mensajeError = 'Solo letras. Mínimo 2 caracteres.';
          }
          break;
  
        case 'correo':
          // Expresión regular un poco más robusta para email
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(valor)) {
            mensajeError = 'Ingresa un correo electrónico válido.';
          }
          break;
  
        case 'telefono':
          // Permitir dígitos, espacios, paréntesis, guiones y +
          // Ej. +52 (55) 1234-5678 o 9999999
          if (!/^[\d\s\-\+\(\)]{7,}$/.test(valor)) {
            mensajeError = 'Formato inválido. Solo dígitos, espacios, +, -, (). Mínimo 7 caracteres.';
          }
          break;
  
        case 'empresa':
          // Solo letras, números, espacios y algunos símbolos comunes como & . , -
          // Ajusta a lo que consideres válido para nombres de empresa
          if (!/^[\wÁÉÍÓÚáéíóúñÑ\s\-\.\&,]{2,}$/.test(valor)) {
            mensajeError = 'Ingresa un nombre de empresa válido (mínimo 2 caracteres).';
          }
          break;
  
        case 'cargo':
          // Debe seleccionarse alguna opción (no valor vacío)
          if (valor === '') {
            mensajeError = 'Selecciona un cargo de la lista.';
          }
          break;
  
        case 'mensaje':
          // Permitimos letras, números, puntuación básica, acentos.
          // Exigimos mínimo 10 caracteres para un mensaje. Ajusta según quieras.
          if (!/^[\wÁÉÍÓÚáéíóúñÑ\s\.,;:\-!?()\']{10,}$/m.test(valor)) {
            mensajeError = 'El mensaje debe tener al menos 10 caracteres.';
          }
          break;
      }
  
      // 3. Si hay error, marcarlo
      if (mensajeError) {
        campo.classList.add('error-input');
        errorSpan.textContent = mensajeError;
        errorSpan.style.display = 'block';
        return true; // hay error
      }
      return false;  // no hay error
    }
  
    /**
     * Verifica que el captcha haya sido completado.
     * Para reCAPTCHA v2: grecaptcha.getResponse() regresa un string vacío si no se validó.
     */
    function validarCaptcha() {
      captchaError.textContent = '';
      captchaError.style.display = 'none';
  
      const response = grecaptcha.getResponse();
      if (!response) {
        captchaError.textContent = 'Por favor, completa el reCAPTCHA.';
        captchaError.style.display = 'block';
        return false;
      }
      return true;
    }
  });  