document.addEventListener('DOMContentLoaded', function() {
    // Back to Top Button
    const backToTopButton = document.getElementById('back-to-top');

    // Show/hide back to top button based on scroll position
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
    });

    // Scroll to top when button is clicked
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Mobile Menu Animation
    const menuBtn = document.querySelector('.header-menuImg');
    const navList = document.querySelector('.header-nav');
    const body = document.querySelector('body');

    if (menuBtn) {
        menuBtn.addEventListener('click', function() {
            body.classList.toggle('menu-open');
            navList.classList.toggle('header-nav__active');
            body.classList.toggle('stop-scroll');
        });
    }

    // Close menu when clicking on links
    navList.addEventListener('click', (event) => {
        if (event.target.classList.contains('header-nav__link')) {
            navList.classList.remove('header-nav__active');
            body.classList.remove('stop-scroll');
            body.classList.remove('menu-open');
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (navList.classList.contains('header-nav__active') &&
            !navList.contains(event.target) &&
            !menuBtn.contains(event.target)) {
            navList.classList.remove('header-nav__active');
            body.classList.remove('stop-scroll');
            body.classList.remove('menu-open');
        }
    });

    // Form styling is handled by form.js

    // Add micro-interactions to service items
    const serviceItems = document.querySelectorAll('.service-item');
    serviceItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});


// Accordion functionality
const accordionItems = document.querySelectorAll(".accordion-item");
const accordionArrow = document.querySelectorAll('.accordion-header img');

accordionItems.forEach((item, index) => {
    const header = item.querySelector(".accordion-header");
    const content = item.querySelector(".accordion-content");

    header.addEventListener("click", function () {
        if (!content.classList.contains('accordion-active')) {
            content.classList.add('accordion-active');
            accordionArrow[index].classList.add('image-rotate')
        } else {
            content.classList.remove('accordion-active');
            accordionArrow[index].classList.remove('image-rotate')
        }
    });
});

// Language selection functionality
const languageSelect = () => {
    const languageInnner = document.querySelector('.header-list__lang');
    const languages = document.querySelectorAll('.header-lang');
    languageInnner.addEventListener('click', (event) => {
        if (event.target.classList.contains('header-lang') && event.target.classList.contains('header-lang__active')) {
            languages.forEach((lang) => {
                lang.classList.remove('header-lang__active');
                lang.classList.add('lang-visible');
            })
        }
    })
    languages.forEach((lang, index) => {
        lang.addEventListener('click', () => {
            lang.classList.add('header-lang__active');
            languages.forEach((otherLang, otherIndex) => {
                if (index === otherIndex) {
                    lang.classList.add('header-lang__active');
                    otherLang.classList.add('lang-visible');
                } else {
                    otherLang.classList.remove('header-lang__active');
                    otherLang.classList.remove('lang-visible');
                }
            });
        });
    });
}

languageSelect()
