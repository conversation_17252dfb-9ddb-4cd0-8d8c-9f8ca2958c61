document.addEventListener('DOMContentLoaded', () => {
    const testimonios = document.querySelectorAll('.testimonio');
    let currentIndex = 0;
  
    const prevBtn = document.getElementById('prevTestimonioBtn');
    const nextBtn = document.getElementById('nextTestimonioBtn');
  
    function mostrarTestimonio(index) {
      // Remover .active de todos
      testimonios.forEach((testimonio) => {
        testimonio.classList.remove('active');
      });
      // Agregar .active al testimonio que queremos ver
      testimonios[index].classList.add('active');
    }
  
    // Botón Anterior
    prevBtn.addEventListener('click', () => {
      currentIndex--;
      if (currentIndex < 0) {
        currentIndex = testimonios.length - 1;
      }
      mostrarTestimonio(currentIndex);
    });
  
    // Botón Siguiente
    nextBtn.addEventListener('click', () => {
      currentIndex++;
      if (currentIndex >= testimonios.length) {
        currentIndex = 0;
      }
      mostrarTestimonio(currentIndex);
    });
  
    // (Opcional) Cambiar automáticamente cada 5 segundos:
    setInterval(() => {
      currentIndex = (currentIndex + 1) % testimonios.length;
      mostrarTestimonio(currentIndex);
    }, 6000);
  });
  