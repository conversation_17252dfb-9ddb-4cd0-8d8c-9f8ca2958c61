// Valores predeterminados fijos (no se muestran en el formulario)
const EMPRESA = "STI Logistics";
const SITIO = "https://stilogistica.mx";
const LOGO = "https://stilogistica.mx/images/logo.png";

document.getElementById("generarFirma").addEventListener("click", () => {
  const nombre = document.getElementById("nombre").value.trim() || "";
  const apellido = document.getElementById("apellido").value.trim() || "";
  const correo = document.getElementById("correo").value.trim() || "";
  const telefono = document.getElementById("telefono").value.trim() || "";

  // Construimos la firma con la estructura solicitada
  const firma = `
<table
  cellpadding="0"
  cellspacing="0"
  style="
    vertical-align: -webkit-baseline-middle;
    font-size: medium;
    font-family: 'Trebuchet MS';
  "
>
  <tbody>
    <!-- Fila 1: Nombre, empresa, divisor y datos de contacto -->
    <tr>
      <td>
        <table
          cellpadding="0"
          cellspacing="0"
          style="
            vertical-align: -webkit-baseline-middle;
            font-size: medium;
            font-family: 'Trebuchet MS';
          "
        >
          <tbody>
            <tr>
              <!-- Columna Izquierda: Nombre completo y Empresa -->
              <td style="vertical-align: middle">
                <h2
                  style="
                    margin: 0px;
                    font-size: 18px;
                    color: #000000;
                    font-weight: 600;
                  "
                >
                  ${nombre} ${apellido}
                </h2>
                <p
                  style="
                    margin: 0px;
                    font-weight: 500;
                    color: #000000;
                    font-size: 14px;
                    line-height: 22px;
                  "
                >
                  ${EMPRESA}
                </p>
              </td>

              <!-- Espaciado -->
              <td width="30"><div style="width: 30px"></div></td>

              <!-- Divisor vertical -->
              <td
                style="
                  width: 1px;
                  border-left: 1px solid #000188;
                "
              ></td>

              <!-- Espaciado -->
              <td width="30"><div style="width: 30px"></div></td>

              <!-- Columna Derecha: Teléfono, Correo, Sitio Web -->
              <td style="vertical-align: middle">
                <table
                  cellpadding="0"
                  cellspacing="0"
                  style="
                    vertical-align: -webkit-baseline-middle;
                    font-size: medium;
                    font-family: 'Trebuchet MS';
                  "
                >
                  <tbody>
                    ${
                      telefono
                        ? `
                    <!-- Teléfono -->
                    <tr height="25" style="vertical-align: middle">
                      <td width="30" style="vertical-align: middle">
                        <table cellpadding="0" cellspacing="0">
                          <tbody>
                            <tr>
                              <td style="vertical-align: bottom">
                                <span
                                  style="
                                    display: inline-block;
                                    background-color: #000188;
                                  "
                                >
                                  <img
                                    src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/phone-icon-2x.png"
                                    alt="mobilePhone"
                                    width="13"
                                    style="
                                      display: block;
                                      background-color: #000188;
                                    "
                                  />
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                      <td style="padding: 0px; color: #000000">
                        <a
                          href="tel:${telefono}"
                          style="
                            text-decoration: none;
                            color: #000000;
                            font-size: 12px;
                          "
                        >
                          ${telefono}
                        </a>
                      </td>
                    </tr>
                    `
                        : ""
                    }
                    ${
                      correo
                        ? `
                    <!-- Correo -->
                    <tr height="25" style="vertical-align: middle">
                      <td width="30" style="vertical-align: middle">
                        <table cellpadding="0" cellspacing="0">
                          <tbody>
                            <tr>
                              <td style="vertical-align: bottom">
                                <span
                                  style="
                                    display: inline-block;
                                    background-color: #000188;
                                  "
                                >
                                  <img
                                    src="${LOGO}"
                                    alt="emailAddress"
                                    width="13"
                                    style="
                                      display: block;
                                      background-color: #000188;
                                    "
                                  />
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                      <td style="padding: 0px; color: #000000">
                        <a
                          href="mailto:${correo}"
                          style="
                            text-decoration: none;
                            color: #000000;
                            font-size: 12px;
                          "
                        >
                          ${correo}
                        </a>
                      </td>
                    </tr>
                    `
                        : ""
                    }
                    <!-- Sitio Web fijo: stilogistica.mx -->
                    <tr height="25" style="vertical-align: middle">
                      <td width="30" style="vertical-align: middle">
                        <table cellpadding="0" cellspacing="0">
                          <tbody>
                            <tr>
                              <td style="vertical-align: bottom">
                                <span
                                  style="
                                    display: inline-block;
                                    background-color: #000188;
                                  "
                                >
                                  <img
                                    src="https://cdn2.hubspot.net/hubfs/53/tools/email-signature-generator/icons/link-icon-2x.png"
                                    alt="website"
                                    width="13"
                                    style="
                                      display: block;
                                      background-color: #000188;
                                    "
                                  />
                                </span>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                      <td style="padding: 0px; color: #000000">
                        <a
                          href="${SITIO}"
                          style="
                            text-decoration: none;
                            color: #000000;
                            font-size: 12px;
                          "
                        >
                          ${SITIO.replace(/^https?:\/\//, '')}
                        </a>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
    </tr>

    <!-- Fila 2: Línea divisora horizontal -->
    <tr>
      <td>
        <table
          cellpadding="0"
          cellspacing="0"
          style="
            width: 100%;
            vertical-align: -webkit-baseline-middle;
            font-size: medium;
            font-family: 'Trebuchet MS';
          "
        >
          <tbody>
            <tr><td height="30"></td></tr>
            <tr>
              <td
                style="
                  width: 100%;
                  border-bottom: 1px solid #000188;
                  display: block;
                "
              ></td>
            </tr>
            <tr><td height="30"></td></tr>
          </tbody>
        </table>
      </td>
    </tr>

    <!-- Fila 3: Logo Principal (fijo) -->
    <tr>
      <td>
        <table
          cellpadding="0"
          cellspacing="0"
          style="
            width: 100%;
            vertical-align: -webkit-baseline-middle;
            font-size: medium;
            font-family: 'Trebuchet MS';
          "
        >
          <tbody>
            <tr>
              <td style="vertical-align: top">
                <img
                  src="${LOGO}"
                  role="presentation"
                  width="130"
                  style="display: inline-block; max-width: 130px"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </td>
    </tr>

    <tr><td height="30"></td></tr>
  </tbody>
</table>
`;

  // Mostramos la firma en el contenedor
  document.getElementById("preview").innerHTML = firma;
});

// Botón para copiar la firma al portapapeles
document.getElementById("copiarFirma").addEventListener("click", () => {
  const preview = document.getElementById("preview");
  const htmlCode = preview.innerHTML;

  navigator.clipboard
    .writeText(htmlCode)
    .then(() => {
      alert("La firma se ha copiado al portapapeles.");
    })
    .catch((err) => {
      console.error("Error al copiar la firma", err);
    });
});
