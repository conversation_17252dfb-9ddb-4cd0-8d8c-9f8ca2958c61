body {
    margin: 0;
    padding: 20px;
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
  }
  
  .container {
    max-width: 600px;
    margin: 0 auto;
    background: #fff;
    padding: 20px;
    border-radius: 5px;
  }
  
  h1 {
    margin-top: 0;
  }
  
  label {
    display: block;
    margin: 15px 0 5px;
    font-weight: bold;
  }
  
  input {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
  }
  
  button {
    margin-top: 15px;
    padding: 10px 20px;
    background-color: #007bff;
    border: none;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 5px;
  }
  
  button:hover {
    background-color: #0056b3;
  }
  
  h2 {
    margin-top: 40px;
  }
  
  #preview {
    padding: 10px;
    background-color: #fafafa;
    border: 1px dashed #ccc;
    min-height: 200px;
    overflow-x: auto;
  }
  
  .nota {
    font-size: 12px;
    margin-top: 10px;
    color: #999;
  }
  