*,
*::after,
*::before {
    box-sizing: border-box;
}

:root {
    --font-size-12: 0.75rem; /* 12px */
    --font-size-14: 0.875rem; /* 14px */
  }


html {
    scroll-behavior: smooth;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #000F47;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: #00FF00;
    color: #000F47;
    transform: translateY(-3px);
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Outfit', sans-serif;
    background-color: #FFFFFF;
}

a {
    text-decoration: none;
}

ul, li {
    list-style: none;
}

/* Visual Consistency */
.btn-primary {
    background-color: #000F47;
    color: white;
    padding: 15px 30px;
    border: none;
    font-weight: 700;
    transition: all 0.3s ease;
}

.btn-secondary {
    background-color: #00FF00;
    color: #000F47;
    padding: 15px 30px;
    border: none;
    font-weight: 700;
    transition: all 0.3s ease;
}

.btn-primary:hover, .btn-secondary:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.container {
    margin: 0 auto;
    max-width: 1680px;
    padding: 0 20px;
}

.header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    padding: 20px 0;
}

.header-items {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-logo img {
    width: 140px;
}

.header-menuImg {
    display: none;
}

.header-nav {
    display: flex;
    align-items: center;
    transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
}

.header-nav__list {
    margin-right: 40px;
}

.header-nav__list:last-child {
    margin-right: 0;
}

.header-nav__link {
    font-size: 20px;
    font-weight: 400;
    color: #000E49;
    transition: all 0.5s ease-in-out;
    display: inline-block;
}

.header-nav__link:hover {
    transform: scale(1.1);
}

.header-list__lang {
    position: relative;
    width: 118px;
}

.header-lang__inner {
    position: absolute;
    top: -10px;
    z-index: 30;
    background-color: #fff;
}

.header-lang {
    margin: 0 auto 10px;
    display: none;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.header-lang__active {
    display: flex;
}

.lang-visible {
    display: flex;
}

.header-lang__active::before {
    content: '';
    position: absolute;
    right: -34px;
    top: 3px;
    width: 100%;
    height: 100%;
    background-image: url('../images/en-arrow.png');
    background-repeat: no-repeat;
    background-position: center;
}

.header-lang img, span{
    margin-right: 5px;
}

.header-lang span {
    padding-right: 15px;
}

.header-lang button{
    border: none;
    background: transparent;
}


.header-btn {
    font-size: 20px;
    font-weight: 800;
    color: #FFFFFF;
    padding: 15px 35px;
    background-color: #000F47;
    transition: all 0.5s ease-in-out;
}

.header-btn:hover {
    transition: all 0.5s ease-in-out;
    opacity: 0.8;
}

.top {
    background-image: url("../images/bg-top.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 1000px;
    width: 100%;
    position: relative;
}

.top::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000E49;
    clip-path: polygon(0 0, 70% 0, 45% 100%, 0 100%);
    z-index: 0;
}

.top::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(123, 139, 167, 0.09);
    clip-path: polygon(0 0, 48% 0, 13% 100%, 0 100%);
    z-index: 0;
}

.top-inner {
    margin: 0 auto;
    max-width: 1680px;
    padding: 0 20px;
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.top-title {
    font-size: 62px;
    font-weight: 700;
    line-height: 80px;
    color: #FFFFFF;
    max-width: 1056px;
    margin-bottom: 5px;
}

.top-subtitle {
    font-size: 32px;
    font-weight: 500;
    color: #FFFFFF;
    margin-bottom: 15px;
    max-width: 950px;
}

.top-text {
    font-size: 28px;
    font-weight: 300;
    color: #FFFFFF;
    margin-bottom: 40px;
    max-width: 950px;
}

.top-btn {
    font-size: 20px;
    font-weight: 700;
    padding: 15px 0;
    max-width: 283px;
    display: flex;
    justify-content: center;
    color: #000F47;
    background-color: #00FF00;
    transition: all 0.5s ease-in-out;
}

.top-btn:hover {
    opacity: 0.8;
    transition: all 0.5s ease-in-out;
}

.top-texts {
    background-color: #F6F6F6;
    margin-bottom: 100px;
    padding: 5px 0 25px 0;
}

.top-texts__sti {
    font-weight: 300;
    font-size: 62px;
    color: #7B8BA7;
    margin-bottom: 10px;
}

.top-texts__info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-texts__info p {
    font-size: 62px;
    font-weight: 700;
    line-height: 62px;
    color: #000E49;
    max-width: 675px;
    margin-right: 30px;
}

.top-texts__info span {
    max-width: 892px;
    font-size: 28px;
    font-weight: 400;
    line-height: 32px;
    color: #7B8BA7;
}

.service{
    margin-bottom: 100px;
}

.service-title{
    position: relative;
    font-weight: 700;
    font-size: 62px;
    color: #000E49;
    text-align: center;
    margin: 0 auto 60px;
    padding: 15px 0;
    max-width: 621px;
}

.service-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 39%;
    border-bottom: 3px solid #7B8BA7;
    width: 145px;
}

.service-items{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    max-width: 1342px;
    margin: 0 auto;
}

.service-item{
    max-width: 600px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.service-item__pic{
    margin-bottom: 25px;
}

.service-item__pic img{
    max-width: 600px;
    width: 100%;
}

.service-item__title{
    font-size: 28px;
    font-weight: 400;
    color: #000E49;
    margin-bottom: 10px;
    text-align: center;
}

.service-item__text{
    font-size: 20px;
    font-weight: 400;
    text-align: center;
    color: #7B8BA7;
}

.middle{
    background-color: #000E49;
    min-height: 355px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 100px;
    padding: 15px 0;
    position: relative;
}

.middle::after{
    content: "";
    position: absolute;
    clip-path: polygon(82% 0, 100% 0, 100% 100%, 49% 100%);
    background-color: rgba(123, 139, 167, 0.09);
    top: 0;
    width: 100%;
    height: 100%;
}

.middle-items{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.middle-items__text{
    text-align: center;
    font-weight: 500;
    font-size: 62px;
    line-height: 83px;
    color: #FFFFFF;
    margin-bottom: 5px;
}

.middle-items__link {
    font-size: 20px;
    font-weight: 700;
    padding: 15px 0;
    width: 283px;
    display: flex;
    justify-content: center;
    color: #000F47;
    background-color: #00FF00;
    transition: all 0.5s ease-in-out;
}

.middle-items__link:hover {
    opacity: 0.8;
    transition: all 0.5s ease-in-out;
}

.us{
    margin-bottom: 60px;
}

.us-inner{
    margin-bottom: 40px;
}

.us-title{
    font-weight: 700;
    font-size: 62px;
    color: #000E49;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 15px;
}

.us-title::after {
    content: "";
    display: block;
    border: 1px solid #7B8BA7;
    width: 145px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.us-text{
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    color: #7B8BA7;
    text-align: center;
}

.us-items{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.us-item{
    max-width: 340px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.us-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.us-item__pic{
    margin-bottom: 20px;
}

.us-item__pic img{
    width: 140px;
}

.us-item__title{
    font-size: 28px;
    margin-bottom: 10px;
    color: #000E49;
    text-align: center;
}

.us-item__text{
    text-align: center;
    font-size: 20px;
    line-height: 30px;
    color: #7B8BA7;
}

.about{
    display: flex;
    align-items: center;
    margin-bottom: 120px;
}

.about-pics{
    display: flex;
    margin-right: 55px;
}

.about-pic__image{
    margin-right: 15px;
}

.about-pic__image img{
    max-width: 595px;
    width: 100%;
}

.about-pics__images{
    display: flex;
    flex-direction: column;
}

.about-pics__images-two{
    display: none;
}

.about-pics__images img{
    max-width: 300px;
    width: 100%;
}

.about-pics__images img:not(:last-child) {
    margin-bottom: 10px;
}

.about-info{
    max-width: 795px;
    padding: 0 20px;
}

.about-info__title{
    font-weight: 700;
    font-size: 62px;
    color: #000E49;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.about-info__title::after {
    content: "";
    display: block;
    border: 1px solid #7B8BA7;
    width: 90px;
    position: absolute;
    bottom: 12px;
    left: 45px;
    transform: translateX(-50%);
}

.about-info__text{
    font-weight: 400;
    font-size: 20px;
    line-height: 30px;
    color: #7B8BA7;
}

.about-info__text{
    margin-bottom: 40px;
}

.about-info__items{
    margin-top: -20px;
}

.about-info__item{
    display: flex;
    margin-bottom: 20px;
}

.about-info__item-pic{
    margin-right: 18px;
}

.about-info__item-pic img{
    width: 22px;
}

.about-info__item-text span{
    color: #000E49;
    font-size: 28px;
    font-weight: 400;
    display: block;
}

.about-info__item-text p{
    color: #7B8BA7;
    font-size: 20px;
    line-height: 30px;
    font-weight: 400;
}

.consultation{
    margin-bottom: 120px;
    background-image: url("../images/Consultation.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    height: 662px;
}

.consultation::after{
    content: "";
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.consultation-items{
    position: relative;
    z-index: 5;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 662px;
}

.consultation-title{
    font-weight: 700;
    font-size: 96px;
    color: #FFFFFF;
    text-align: center;
}

.consultation-text{
    font-size: 48px;
    font-weight: 400;
    margin-bottom: 20px;
    color: #FFFFFF;
    text-align: center;
}

.consultation-btn {
    font-size: 20px;
    font-weight: 300;
    padding: 13px 63px;
    display: flex;
    justify-content: center;
    color: #000F47;
    background-color: #00FF00;
    transition: all 0.5s ease-in-out;
}

.consultation-btn:hover {
    opacity: 0.8;
    transition: all 0.5s ease-in-out;
}

.questions{
    margin-bottom: 60px;
}

.questions-accordion {
    max-width: 1225px;
    width: 100%;
    margin: 0 auto;
}

.questions-title{
    font-weight: 700;
    font-size: 62px;
    color: #000E49;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 15px;
}

.questions-title::after {
    content: "";
    display: block;
    border: 1px solid #7B8BA7;
    width: 145px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.accordion-item {
    border: 1px solid #7B8BA7;
    margin-bottom: 20px;
    padding: 40px 77px 40px 60px;
    box-shadow: 0 0px 3px rgba(0, 0, 0, 0.5);
    min-height: 151px;
}

.accordion-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    cursor: pointer;
}

.accordion-header span{
    margin-right: 15px;
    color: #000E49;
    font-weight: 400;
    font-size: 28px;
    line-height: 37px;
    max-width: 790px;
}

.accordion-header img {
    transition: all ease-in 0.4s;
    width: 25px;
}

.accordion-content {
    height: 0;
    width: 100%;
    opacity: 0;
    max-width: 960px;
    padding-top: 20px;
    font-size: 20px;
    font-weight: 300;
    line-height: 33px;
    color: #7B8BA7;
    transition: all ease-in 0.4s;
    overflow: hidden;
}

.accordion-active{
    opacity: 1;
    height: 100%;
    transition: all ease-in 0.4s;
}

.image-rotate {
    transform: rotate(180deg);
    transition: all ease-in 0.4s;
}

/* --- Sección de Reseñas --- */
.testimonios-section {
    padding: 50px 0;
    text-align: center;
}

.testimonios-title {
    font-weight: 700;
    color: #000E49;
    font-size: 62px;
    margin-bottom: 30px;
}

.testimonios-container {
    width: 80%;
    max-width: 800px;

    margin: 0 auto;
    position: relative; /* Para que los .testimonio se posicionen dentro */
    min-height: 200px;  /* Ajusta según tu contenido, evita saltos de layout */
}

/* Cada .testimonio, inicialmente oculto (opacity=0) y sin clicks (pointer-events: none) */
.testimonio {
    position: absolute;        /* Se apilan uno sobre otro */
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    pointer-events: none;      /* No se puede interactuar con los inactivos */
    transition: opacity 0.5s ease-in-out;
}

/* Cuando está activo, lo mostramos */
.testimonio.active {
    opacity: 1;
    pointer-events: auto;
    position: relative; /* Para que su contenido mantenga la altura */
}

/* Estilos del texto en cada reseña */
.testimonio blockquote p {
    font-size: 18px;
    line-height: 1.5;
    font-style: italic;
    margin: 0 auto 20px auto;
    max-width: 600px;
}

.testimonio h4 {
    font-size: 20px;
    margin-bottom: 5px;
    font-weight: 600;
}

.testimonio span {
    font-size: 16px;
    color: #555;
}

/* Controles (flechas) */
.testimonios-controles {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 20px; /* separación entre botones */
}

/* Botones < y > */
.testimonios-prev,
.testimonios-next {
    background-color: #03FF00;
    border: none;
    color: #000E49;
    font-size: 24px;
    line-height: 1;
    width: 50px;
    height: 50px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.testimonios-prev:hover,
.testimonios-next:hover {
background-color: #02E600; /* leve oscurecido */
}

.contact{
    margin-bottom: 100px;
    position: relative;
    padding: 150px 0 200px;
}

.contact::before {
    content: '';
    background-image: url('../images/form-bg.jpg');
    background-repeat: no-repeat;
    background-position: right;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
}

.contact-title{
    font-weight: 700;
    font-size: 62px;
    color: #000E49;
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 15px;
}

.contact-title::after {
    content: "";
    display: block;
    border: 1px solid #7B8BA7;
    width: 90px;
    position: absolute;
    bottom: 12px;
    left: 45px;
    transform: translateX(-50%);
}

.contact-text{
    margin-bottom: 20px;
    font-size: 20px;
    line-height: 30px;
    font-weight: 400;
    color: #7B8BA7;
    max-width: 680px;
}

.contact-label{
    display: block;
}

.contact-form__short{
    display: flex;
}

.contact-form__short label{
    margin-right: 40px;
}

.contact-input, .contact-label__textarea, .contact-label__select select{
    font-weight: 400;
    font-size: 17px;
    padding: 15px 31px;
    color: #707070;
    border: 1px solid #000E49;
}

.contact-label__select select{
    appearance: none;
    background-image: url("../images/arrow.svg");
    background-repeat: no-repeat;
    background-position: right 0.7rem top 50%;
    background-size: 1rem auto;
}

.contact-input::placeholder, .contact-label__textarea::placeholder, .contact-label__select select{
    color: #707070;
}

.contact-input__short{
    width: 320px;
}

.contact-label__checkbox{
    display: flex;
    align-items: center;
}

label.contact-label__checkbox input{
    border: 1px solid #000E49;
    width: 22px;
}

.contact-label__checkbox span{
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 15px;
    color: #000E49;
    padding-left: 15px;
}

.contact-btn{
    font-size: 20px;
    font-weight: 300;
    max-width: 681px;
    width: 100%;
    padding: 15px;
    background-color: #03FF00;
    border: none;
}

.contact-input__short{
    width: 320px;
}

.contact-input__long, .contact-label__select select, .contact-label__textarea{
    width: 681px;
}

.checkbox-container {
    display: block;
    position: relative;
    padding-left: 35px;
    margin-bottom: 30px;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.contact-label__checkbox-span{
    display: none;
}

.contact-mobile__img {
    display: none;
}

/* Form Improvements */
.input-group {
    position: relative;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    min-height: 72px;
}

.error-message {
    color: #e74c3c;
    font-size: 14px;
    margin-top: 5px;
    font-weight: 500;
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.contact-input.error-input,
.contact-label__textarea.error-input {
    border: 1px solid #e74c3c;
    background-color: rgba(231, 76, 60, 0.05);
    outline: none;
}

.contact-input.valid-input,
.contact-label__textarea.valid-input {
    border: 1px solid #2ecc71;
    background-color: rgba(46, 204, 113, 0.05);
}

/* Button Loader */
.form-submit-container {
    position: relative;
}

.contact-btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-loader {
    position: absolute;
}

.spinner {
    animation: rotate 2s linear infinite;
    width: 20px;
    height: 20px;
}

.path {
    stroke: #ffffff;
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    width: 25px;
    background-color: #eee;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #2196F3;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 9px;
    top: 5px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

.input-placeholder__color::placeholder {
    color: red;
}

.footer{
    background-color: #01184A;
    padding-top: 45px;

}

.footer-inner{
    padding-bottom: 40px;
}

.footer-bottom{
    background-color: #7B8BA7;
    padding: 30px 20px;
    margin: 0 auto;
}

.footer-bottom__inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 0;
}

.footer-bottom__text{
    font-weight: 400;
    font-size: 18px;
    color: #FFFFFF;
    text-align: center;
    font-family: 'Open Sans', sans-serif;
}

/* Social Media Sharing */
.social-share {
    display: flex;
    align-items: center;
    margin: 20px 0;
}

.social-share__title {
    font-size: 18px;
    font-weight: 500;
    margin-right: 15px;
    color: #000E49;
}

.social-share__links {
    display: flex;
}

.social-share__link {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #000E49;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-share__link:hover {
    transform: translateY(-3px);
    background-color: #00FF00;
}

.social-share__link svg {
    width: 20px;
    height: 20px;
    fill: white;
}

@media (max-width: 768px) {
    .footer-bottom__inner {
        flex-direction: column;
        text-align: center;
    }

    .social-share {
        margin-top: 15px;
        flex-direction: column;
    }

    .social-share__title {
        margin-bottom: 10px;
        margin-right: 0;
    }
}

.footer-inner{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.footer-logo{
    margin-bottom: 20px;
}

.footer-logo img{
    width: 190px;
}

.footer-text{
    max-width: 494px;
    font-size: 18px;
    line-height: 26px;
    color: #FFFFFF;
    margin-bottom: 45px;
}

.footer-info__wp{
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    z-index: 10;
}

.footer-info__wp img{
    width: 81px;
    margin-right: 5px;
}

.footer-info__wp span{
    font-weight: 700;
    font-size: 22px;
    color: #ffffff;
}

.footer-items{
    display: flex;
    max-width: 995px;
    width: 100%;
    justify-content: space-between;
}

.footer-item__title{
    margin-bottom: 30px;
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    position: relative;
}

.footer-item__title::after {
    content: "";
    display: block;
    border: 1px solid #ffffff;
    width: 176px;
    position: absolute;
    bottom: -10px;
    left: 51%;
    transform: translateX(-50%);
}

.footer-item__list{
    margin-bottom: 25px;
}

.footer-item__link{
    font-weight: 300;
    font-size: 18px;
    color: #ffffff;
    display: block;
    transition: all 0.5s ease-in-out;
}

.footer-item__link:hover{
    transform: scale(1.1);
}

.footer-contact__title{
    margin-bottom: 30px;
    color: #ffffff;
    font-size: 22px;
    font-weight: 700;
    position: relative;
}

.footer-contact{
    display: flex;
    flex-direction: column;
}

.footer-contact__title::after {
    content: "";
    display: block;
    border: 1px solid #ffffff;
    width: 292px;
    position: absolute;
    bottom: -10px;
    left: 53%;
    transform: translateX(-50%);
}

.footer-contact__link{
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.footer-contact__link img{
    width: 20px;
    margin-right: 20px;
}

.footer-contact__link span{
    font-size: 18px;
    font-weight: 300;
    color: #ffffff;
}



@media (max-width: 1820px){
    .contact{
        position: relative;
    }
    .contact-inner{
        position: relative;
        z-index: 2;
    }
    .contact::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgb(255, 255, 255);
        clip-path: polygon(0 0, 70% 0, 45% 100%, 0 100%);
        z-index: 0;
    }
}

@media (max-width: 1665px){
    .top-title{
        max-width: 760px;
        font-size: 37px;
        line-height: 48px;
    }
    .top-text{
        max-width: 760px;
        font-size: 20px;
        margin-bottom: 20px;
    }
    .top-btn{
        max-width: 230px;
    }
    .top-texts__info p{
        font-size: 50px;
    }
    .top-texts__info span{
        max-width: 828px;
    }
    .footer-items{
        max-width: 800px;
    }
    .footer-text{
        max-width: 353px;
    }
    .contact-input__long, .contact-label__select select, .contact-label__textarea, .contact-btn{
        width: 500px;
    }
    .contact-form__short label{
        margin-right: 20px;
    }
    .contact-input__short{
        width: 240px;
    }
    .contact-text{
        max-width: 600px;
    }
}

@media (max-width: 1440px) {
    .header-nav__list:not(:last-child) {
        margin-right: 16px;
    }
    .top-texts__sti, .top-texts__info p{
        font-size: 40px;
    }
    .top-texts__info span{
        font-size: 22px;
        max-width: 735px;
    }
}

@media (max-width: 1320px) {
    .header-nav__link {
        font-size: 16px;
    }

    .header-lang {
        margin-left: 10px;
    }
    .header-btn {
        font-size: 16px;
        padding: 10px 20px;
    }
    .header-logo img {
        width: 110px;
    }
    .service-item{
        max-width: 435px;
    }
    .about{
        flex-direction: column;
    }
    .about-pics{
        margin-bottom: 65px;
        margin-right: 0;
    }
}

@media (max-width: 1240px){
    .top-texts__sti{
        text-align: center;
    }
    .top-texts__info{
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    .top-texts__info p{
        margin-right: 0;
    }
    .middle-items__text{
        font-size: 50px;
        line-height: 71px;
    }
    .footer-inner, .footer-items, .footer-info__wp{
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }
    .footer-text{
        margin-bottom: 100px;
        color: #ffffff;
        font-size: 22px;
        font-weight: 700;
        position: relative;
        max-width: 495px;
    }
    .footer-text::after {
        content: "";
        display: block;
        border: 1px solid #ffffff;
        width: 353px;
        position: absolute;
        bottom: -65px;
        left: 50%;
        transform: translateX(-50%);
    }
    .footer-info__wp img{
        margin: 0 0 13px 0;
    }
    .footer-contact__title::after{
        width: 176px;
    }
    .footer-item__title{
        margin-bottom: 35px;
    }
    .footer-item{
        margin-bottom: 10px;
    }
    .footer-logo{
        margin-bottom: 40px;
    }
}

@media(max-width: 1078px) {
    .contact::after{
        clip-path: polygon(0 0, 82% 0, 50% 100%, 0 100%);
    }
}

@media (max-width: 1024px) {
    .header-nav {
        position: fixed;
        top: 0;
        left: -100%;
        z-index: 20;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        width: 100%;
        height: 100vh;
        transition: left 0.4s cubic-bezier(0.77, 0.2, 0.05, 1.0);
    }
    .header-menuImg {
        display: block;
        position: relative;
        z-index: 21;
        transition: transform 0.3s ease;
    }
    .menu-open .header-menuImg {
        transform: rotate(90deg);
    }
    .header-nav__active {
        left: 0;
    }
    .stop-scroll {
        overflow: hidden;
    }
    .header-nav__list{
        margin: 0 0 40px 0;
    }
    .header-nav__list:not(:last-child){
        margin-right: 0;
    }
    .top-title{
        max-width: 458px;
        font-size: 27px;
        line-height: 38px;
    }
    .top-text{
        max-width: 300px;
        font-size: 20px;
        margin-bottom: 20px;
    }
    .service-items{
        flex-direction: column;
        align-items: center;
    }
    .service-title{
        max-width: 300px;
    }
    .service-title::after{
        left: 28%;
    }
}

@media (max-width: 948px){
    .contact::after {
        clip-path: polygon(0 0, 103% 0, 63% 100%, 0 100%);
    }
}

@media (max-width: 768px) {
    .top {
        background-position: 85%;
    }
    .top::before {
        clip-path: polygon(0 33%, 100% 55%, 100% 100%, 0% 100%);
    }
    .top::after {
        clip-path: polygon(0 40%, 100% 56%, 100% 100%, 0% 100%);
    }
    .top{
        height: 765px;
    }
    .top-inner{
        align-items: center;
        padding-bottom: 50px;
        justify-content: flex-end;
    }
    .top-title{
        max-width: 353px;
        font-size: 28px;
        line-height: 46px;
        margin-bottom: 17px;
    }
    .top-subtitle {
        font-size: 24px;
        line-height: 32px;
    }
    .top-text{
        max-width: 353px;
        font-size: 16px;
        line-height: 29px;
        margin-bottom: 23px;
        text-align: center;
    }
    .top-texts__info {
        flex-direction: column;
    }
    .top-texts__info p {
        font-size: 32px;
        line-height: 38px;
        margin-bottom: 20px;
    }
    .top-texts__info span {
        font-size: 16px;
        line-height: 22px;
    }
    .top-btn{
        max-width: 283px;
        font-size: 16px;
        padding: 20px 82px;
    }
    .top-texts{
        padding: 25px 0 60px 0;
        margin-bottom: 0;
        background-color: #ffffff;
    }
    .top-texts__sti{
        font-size: 28px;
        margin-bottom: 10px;
    }
    .top-texts__info p{
        font-size: 28px;
        line-height: 37px;
        margin-bottom: 12px;
    }
    .top-texts__info span{
        font-size: 14px;
        line-height: 20px;
    }
    .service{
        margin-bottom: 55px;
    }
    .service-title{
        font-size: 28px;
        margin-bottom: 20px;
    }
    .service-title::after{
        left: 40%;
        bottom: 12px;
        width: 63px;
    }
    .service-item__title{
        font-size: 20px;
    }
    .service-item__text{
        font-size: 14px;
        line-height: 20px;
    }
    .service-item{
        margin-bottom: 55px;
    }
    .service-item:last-child{
        margin-bottom: 0;
    }
    .middle{
        min-height: 470px;
        margin-bottom: 80px;
    }
    .middle-items__text{
        font-size: 28px;
        line-height: 37px;
        margin-bottom: 15px;
    }
    .middle-items__link{
        font-weight: 300;
    }
    .middle::after{
        clip-path: polygon(168% 0, 100% 0, 100% 100%, -15% 100%);
    }
    .us-inner{
        margin-bottom: 75px;
    }
    .us-title{
        font-size: 32px;
    }
    .us-items{
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .us-item__title{
        font-size: 20px;
    }
    .us-item__text{
        font-size: 14px;
        line-height: 20px;
        font-weight: 300;
    }
    .about{
        margin-bottom: 80px;
    }
    .about-pics{
        flex-direction: column;
    }
    .about-pic__image{
        margin-right: 0;
    }
    .about-pics__images-two{
        display: block;
    }
    .about-pics__images-one{
        display: none;
    }
    .about-pic__image img{
        max-width: 768px;
        margin-bottom: 10px;
    }
    .about-pics__images img{
        max-width: 768px;
    }
    .about-info__title{
        font-size: 37px;
        text-align: center;
    }
    .about-info__title::after{
        left: 50%;
    }
    .about-info__text{
        font-weight: 300;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 25px;
    }
    .about-info__items{
        margin-top: 15px;
    }
    .about-info__item-pic img{
        width: 18px;
    }
    .about-info__item-text span{
        font-size: 20px;
    }
    .about-info__item-text p{
        font-size: 14px;
        line-height: 20px;
        font-weight: 300;
    }
    .consultation{
        margin-bottom: 80px;
    }
    .consultation, .consultation-items{
        height: 406px;
    }
    .consultation-title{
        font-size: 28px;
    }
    .consultation-text{
        font-size: 16px;
        line-height: 21px;
        margin-bottom: 60px;
    }
    .consultation-btn{
        font-weight: 700;
    }
    .questions{
        margin-bottom: 80px;
    }
    .accordion-item{
        padding: 18px 18px 28px 14px;
    }
    .accordion-header span{
        font-size: 20px;
    }
    .accordion-content{
        font-size: 14px;
        line-height: 20px;
    }
    .questions-title{
        font-size: 28px;
    }
    .testimonios-title{
        font-size: 28px;
    }
    .contact {
        margin-bottom: 80px;
    }
    .footer-text{
        font-size: 14px;
        line-height: 26px;
        font-weight: 300;
    }
    .footer-info__wp span, .footer-item__title{
        font-size: 20px;
    }
    .footer-item__link{
        font-size: 14px;
    }
    .contact {
        padding: 50px 0 0;
        background-color: #F6F6F6;
    }
    .contact-title{
        text-align: center;
        font-size: 28px;
    }
    .contact-title::after{
        left: 50%;
    }
    .contact-text{
        text-align: center;
        font-size: 14px;
        font-weight: 300;
        margin: 0 auto 40px auto;
    }
    .contact-form__short{
        display: block;
    }
    .contact-input, .contact-input__short, .contact-label__select select, .contact-label__textarea{
        width: 350px;
    }
    .contact-form{
        text-align: center;
    }
    .contact-form__short label{
        margin-right: 0;
    }
    .contact-label__checkbox{
        justify-content: center;
    }
    .contact-label{
        margin-bottom: 20px;
    }
    .contact-label__checkbox-first{
        display: none;
    }
    .contact-label__checkbox-span{
        display: inline;
    }
    .contact-btn {
        max-width: 320px;
    }
    .contact::before {
        display: none;
    }
    .contact::after {
        display: none;
    }
    .contact-inner {
        margin-bottom: 75px;
    }
    .contact-mobile__img {
        display: block;
        background-image: url('../images/form-bg.jpg');
        background-repeat: no-repeat;
        background-size: auto;
        background-position: right;
        width: 100%;
        height: 560px;
        position: relative;
    }
    .contact-mobile__img::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background-color: #F6F6F6;
        clip-path: polygon(0 0, 100% 0, 100% 0%, 0 40%);
    }
}


