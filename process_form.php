<?php
// Incluir la configuración
require 'config.php';

// Configurar cabeceras para evitar caché y establecer el contenido como JSON
header('Content-Type: application/json');

// Función para limpiar y validar los datos de entrada
function sanitize_input($data) {
    return htmlspecialchars(stripslashes(trim($data)));
}

// Función para validar datos con reglas específicas
function validate_input($data, $type) {
    $data = sanitize_input($data);
    $error = null;

    switch ($type) {
        case 'nombre':
        case 'apellido':
            // Solo letras, espacios y acentos/ñ. Mínimo 2 caracteres.
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!preg_match('/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,}$/', $data)) {
                $error = 'Solo se permiten letras y espacios (mínimo 2 caracteres).';
            }
            break;

        case 'email':
            if (empty($data)) {
                $error = 'El correo electrónico es obligatorio.';
            } elseif (!filter_var($data, FILTER_VALIDATE_EMAIL)) {
                $error = 'Formato de correo electrónico inválido.';
            }
            break;

        case 'telefono':
            if (empty($data)) {
                $error = 'El teléfono es obligatorio.';
            } elseif (!preg_match('/^[\d\s\-\+\(\)]{7,}$/', $data)) {
                $error = 'Formato de teléfono inválido.';
            }
            break;

        case 'empresa':
            if (empty($data)) {
                $error = 'El nombre de la empresa es obligatorio.';
            } elseif (!preg_match('/^[\wÁÉÍÓÚáéíóúñÑ\s\-\.\&,]{2,}$/u', $data)) {
                $error = 'Formato de nombre de empresa inválido.';
            }
            break;

        case 'cargo':
            if (empty($data)) {
                $error = 'Selecciona un cargo.';
            }
            break;

        case 'mensaje':
            if (empty($data)) {
                $error = 'El mensaje es obligatorio.';
            } elseif (strlen($data) < 10) {
                $error = 'El mensaje debe tener al menos 10 caracteres.';
            }
            break;
    }

    return ['value' => $data, 'error' => $error];
}

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Inicializar array de errores
    $errors = [];

    // Validar cada campo
    $nombre_result = validate_input(isset($_POST['nombre']) ? $_POST['nombre'] : '', 'nombre');
    $nombre = $nombre_result['value'];
    if ($nombre_result['error']) $errors['nombre'] = $nombre_result['error'];

    $apellido_result = validate_input(isset($_POST['apellido']) ? $_POST['apellido'] : '', 'apellido');
    $apellido = $apellido_result['value'];
    if ($apellido_result['error']) $errors['apellido'] = $apellido_result['error'];

    $correo_result = validate_input(isset($_POST['correo']) ? $_POST['correo'] : '', 'email');
    $correo = $correo_result['value'];
    if ($correo_result['error']) $errors['correo'] = $correo_result['error'];

    $telefono_result = validate_input(isset($_POST['telefono']) ? $_POST['telefono'] : '', 'telefono');
    $telefono = $telefono_result['value'];
    if ($telefono_result['error']) $errors['telefono'] = $telefono_result['error'];

    $empresa_result = validate_input(isset($_POST['empresa']) ? $_POST['empresa'] : '', 'empresa');
    $empresa = $empresa_result['value'];
    if ($empresa_result['error']) $errors['empresa'] = $empresa_result['error'];

    $cargo_result = validate_input(isset($_POST['cargo']) ? $_POST['cargo'] : '', 'cargo');
    $cargo = $cargo_result['value'];
    if ($cargo_result['error']) $errors['cargo'] = $cargo_result['error'];

    $mensaje_result = validate_input(isset($_POST['mensaje']) ? $_POST['mensaje'] : '', 'mensaje');
    $mensaje = $mensaje_result['value'];
    if ($mensaje_result['error']) $errors['mensaje'] = $mensaje_result['error'];

    $recaptcha_response = isset($_POST['g-recaptcha-response']) ? $_POST['g-recaptcha-response'] : '';

    // Si hay errores, devolver respuesta con errores
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Hay errores en el formulario.',
            'errors' => $errors
        ]);
        exit;
    }

    // Verificar reCAPTCHA
    $recaptcha_secret = RECAPTCHA_SECRET; // Tu secret key en config.php
    $recaptcha_url    = 'https://www.google.com/recaptcha/api/siteverify';

    $response = file_get_contents($recaptcha_url . '?secret=' . $recaptcha_secret . '&response=' . $recaptcha_response);
    $responseKeys = json_decode($response, true);

    if (!$responseKeys["success"]) {
        http_response_code(400);
        echo json_encode(['message' => 'Error en la verificación de reCAPTCHA.']);
        exit;
    }

    // Conectar a la base de datos
    try {
        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_DATABASE);

        // Verificar la conexión
        if ($mysqli->connect_errno) {
            // Registrar el error en un archivo de log
            error_log("Error de conexión a la base de datos: " . $mysqli->connect_error);

            // Devolver un mensaje genérico al usuario (sin exponer detalles sensibles)
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Hubo un problema al procesar tu solicitud. Por favor, intenta de nuevo más tarde.'
            ]);
            exit;
        }

        // Establecer el conjunto de caracteres
        $mysqli->set_charset("utf8mb4");
    } catch (Exception $e) {
        // Registrar el error en un archivo de log
        error_log("Excepción al conectar a la base de datos: " . $e->getMessage());

        // Devolver un mensaje genérico al usuario
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Hubo un problema al procesar tu solicitud. Por favor, intenta de nuevo más tarde.'
        ]);
        exit;
    }

    // Preparar la sentencia SQL para prevenir inyecciones SQL
    $stmt = $mysqli->prepare("
        INSERT INTO contactos (nombre, apellido, correo, telefono, empresa, cargo, mensaje)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    if (!$stmt) {
        http_response_code(500);
        echo json_encode(['message' => 'Error al preparar la consulta.']);
        exit;
    }

    // Vincular los parámetros y ejecutar
    $stmt->bind_param("sssssss", $nombre, $apellido, $correo, $telefono, $empresa, $cargo, $mensaje);

    if ($stmt->execute()) {
        // ==============================
        // 1. Armamos el cuerpo del correo
        // ==============================
        $destinatario = EMAIL_TO; // Configurado en config.php
        $asunto = 'Nuevo Contacto desde el Formulario';

        // Mensaje en texto plano (puedes agregar HTML si gustas)
        $cuerpoMensaje =
            "Has recibido un nuevo mensaje:\n\n" .
            "Nombre:   $nombre $apellido\n" .
            "Correo:   $correo\n" .
            "Teléfono: $telefono\n" .
            "Empresa:  $empresa\n" .
            "Cargo:    $cargo\n\n" .
            "Mensaje:\n$mensaje\n";

        // 2. Cabeceras (para 'From' y 'Reply-To')
        // IMPORTANTE: Para evitar filtrado de spam, a veces conviene usar
        // 'From: <EMAIL>' en lugar del correo del usuario.
        // Y 'Reply-To' para que puedas responderle directamente.
        $headers  = "From: " . EMAIL_FROM . "\r\n";
        $headers .= "Reply-To: " . $correo . "\r\n";
        $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

        // 3. Enviamos el correo con la función nativa mail()
        // Ten en cuenta que la entrega puede depender de la configuración
        // del servidor (sendmail, postfix, etc.).
        if (mail($destinatario, $asunto, $cuerpoMensaje, $headers)) {
            http_response_code(200);
            echo json_encode(['message' => 'Formulario enviado exitosamente.']);
        } else {
            http_response_code(500);
            echo json_encode(['message' => 'No se pudo enviar el correo mediante mail().']);
        }

    } else {
        http_response_code(500);
        echo json_encode(['message' => 'Error al guardar los datos en la base de datos.']);
    }

    // Cerrar conexiones
    $stmt->close();
    $mysqli->close();

} else {
    // Si no es una solicitud POST, rechazar
    http_response_code(405);
    echo json_encode(['message' => 'Método de solicitud no permitido.']);
    exit;
}
?>
