# Cotizar <PERSON> - STI Logistics

## Descripción
Módulo de solicitud de cotización para servicios logísticos de STI Logistics. Permite a los clientes enviar solicitudes de cotización con información detallada sobre su carga y rutas.

## Características

### Campos del Formulario
1. **Empresa** - Nombre de la empresa solicitante
2. **Nombre** - Nombre del contacto
3. **Dirección Origen** - Dirección completa de origen
4. **CP Origen** - Código postal de origen (5 dígitos)
5. **Dirección Destino** - Dirección completa de destino
6. **CP Destino** - Código postal de destino (5 dígitos)
7. **Piezas** - Número de piezas a transportar
8. **Peso** - Peso total en kilogramos
9. **Dimensiones** - Dimensiones en formato "Largo x Ancho x Alto (cm)"
10. **Descripción de la Mercancía** - Descripción detallada
11. **Tipo de Mercancía** - Selección múltiple:
    - Sobredimensionado
    - Hazmat (Materiales Peligrosos)
    - Perecedero
    - Carga General

### Funcionalidades
- **Validación en tiempo real** - Los campos se validan mientras el usuario escribe
- **Disclaimer obligatorio** - Se muestra un aviso antes del envío que debe ser aceptado
- **Diseño responsivo** - Compatible con dispositivos móviles y desktop
- **Envío por email** - Los datos se envían a múltiples destinatarios configurados
- **Mensajes de confirmación** - Feedback visual del estado del envío

### Flujo de Usuario
1. El usuario completa todos los campos del formulario
2. Hace clic en "Solicitar Cotización"
3. Se muestra el disclaimer obligatorio
4. El usuario debe aceptar el disclaimer
5. Se habilita el botón "Enviar Solicitud"
6. Al enviar, se procesa la información y se envía por email

## Archivos del Módulo

### `index.html`
- Estructura HTML del formulario
- Campos organizados en secciones lógicas
- Elementos para mostrar errores de validación
- Sección de disclaimer con checkbox

### `styles.css`
- Estilos CSS responsivos
- Diseño consistente con la marca STI
- Animaciones y transiciones suaves
- Estados de error y éxito

### `script.js`
- Validación de formulario en tiempo real
- Lógica del disclaimer
- Manejo de envío AJAX
- Mensajes de feedback al usuario

### `process_quote.php`
- Procesamiento del formulario en el servidor
- Validación de datos del lado del servidor
- Envío de emails a múltiples destinatarios
- Respuestas JSON para el frontend

## Configuración

### Requisitos
- PHP 7.0 o superior
- Función `mail()` habilitada en el servidor
- Configuración de email en `../config.php`

### Variables de Configuración
El módulo utiliza las siguientes variables del archivo `config.php`:
- `EMAIL_FROM` - Email remitente
- `EMAIL_TO` - Email destinatario principal

### Personalización de Destinatarios
Para agregar más destinatarios de email, editar el array `$recipients` en `process_quote.php`:

```php
$recipients = [
    EMAIL_TO,
    '<EMAIL>',
    '<EMAIL>'
];
```

## Compatibilidad
- Compatible con hosting compartido (Hostinger)
- No requiere Node.js ni tecnologías del lado del servidor avanzadas
- Utiliza solo HTML, CSS, JavaScript y PHP estándar

## Instalación
1. Copiar la carpeta `cotizar-servicio` al directorio web
2. Asegurar que `../config.php` esté configurado correctamente
3. Verificar que la función `mail()` esté habilitada
4. Acceder a `cotizar-servicio/index.html` en el navegador

## Validación Mejorada

### Validación en Tiempo Real
- **Validación mientras escribe**: Los campos se validan automáticamente mientras el usuario escribe (con debounce de 300ms)
- **Validación al salir del campo**: Validación inmediata cuando el usuario sale de un campo
- **Indicadores visuales**: Campos válidos muestran borde verde, campos con errores muestran borde rojo
- **Mensajes específicos**: Cada tipo de error tiene un mensaje específico y útil en español

### Reglas de Validación Específicas

#### Empresa y Nombre
- Mínimo 2 caracteres, máximo 100/80 caracteres respectivamente
- Solo letras, números y símbolos básicos permitidos
- Mensajes específicos para cada tipo de error

#### Direcciones
- Mínimo 15 caracteres para asegurar especificidad
- Máximo 200 caracteres
- Validación de caracteres permitidos para direcciones mexicanas

#### Códigos Postales
- Exactamente 5 dígitos
- Rango válido para México (1000-99999)
- Auto-formateo: solo permite números

#### Tipo de Mercancía
- Validación de opciones predefinidas
- Manejo especial para elementos select

#### Piezas
- Solo números enteros positivos
- Rango: 1 a 10,000 piezas
- Auto-formateo: elimina caracteres no numéricos

#### Peso
- Números decimales con máximo 2 decimales
- Rango: 0.1 a 50,000 kg
- Auto-formateo para decimales

#### Dimensiones
- Formato específico: "Largo x Ancho x Alto"
- Auto-formateo mientras escribe
- Validación de rangos (1-2000 cm por dimensión)
- Acepta variaciones: "x", "X", "×"

#### Descripción
- Mínimo 10 caracteres, máximo 500
- Validación de caracteres permitidos
- Mensajes específicos para longitud

### Características de UX

#### Estado del Formulario
- **Botón deshabilitado**: El botón "Solicitar Cotización" permanece deshabilitado hasta que todos los campos sean válidos
- **Indicador visual**: Botón gris cuando está deshabilitado
- **Seguimiento de estado**: Sistema interno rastrea el estado de validación de cada campo

#### Flujo de Disclaimer
- Solo aparece cuando todos los campos son válidos
- Scroll automático al disclaimer
- Checkbox obligatorio para habilitar envío

#### Manejo de Errores
- **Scroll al primer error**: Cuando hay errores, automáticamente hace scroll al primer campo con error
- **Foco automático**: Coloca el cursor en el primer campo con error
- **Mensajes persistentes**: Los errores permanecen visibles hasta ser corregidos

#### Auto-formateo
- **Códigos postales**: Solo permite 5 dígitos
- **Peso**: Formatea automáticamente decimales
- **Dimensiones**: Agrega "x" entre números automáticamente
- **Piezas**: Solo permite números enteros

### Archivos de Prueba
- `test-validation.html`: Suite de pruebas para validar funcionalidad
- Casos de prueba manuales incluidos
- Funciones de ayuda para llenar datos de prueba

## Seguridad
- Validación tanto en cliente como en servidor
- Sanitización de datos de entrada
- Protección contra inyección de código
- Headers de seguridad en respuestas JSON
- Validación de rangos para prevenir valores extremos
