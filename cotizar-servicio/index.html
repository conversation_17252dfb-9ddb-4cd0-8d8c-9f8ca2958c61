<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title><PERSON><PERSON><PERSON></title>

  <!-- Main site styles for navbar -->
  <link rel="stylesheet" href="../css/reset.css" />
  <link rel="stylesheet" href="../css/style.css" />
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans&family=Outfit:wght@300;400;500;700;800&family=Poppins&display=swap" rel="stylesheet" />
  <link rel="shortcut icon" href="../images/favicon-light.png" type="image/x-icon" />

  <!-- Module-specific styles -->
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <!-- Back to top button -->
  <button id="back-to-top" class="back-to-top" aria-label="Volver arriba">
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12 19V5M5 12l7-7 7 7"/>
    </svg>
  </button>

  <!-- Header Navigation -->
  <header class="header">
    <div class="container">
      <div class="header-items">
        <a href="../index.html" class="header-logo">
          <img src="../images/logo.png" alt="STI Logística - Logo oficial">
        </a>
        <img src="../images/menu.svg" alt="Menú de navegación" class="header-menuImg">
        <nav class="header-nav">
          <li class="header-nav__list">
            <a href="../index.html" class="header-nav__link">Inicio</a>
          </li>
          <li class="header-nav__list">
            <a href="../index.html#servicios" class="header-nav__link">Servicios</a>
          </li>
          <li class="header-nav__list">
            <a href="../index.html#elegirnos" class="header-nav__link">¿Por qué elegirnos?</a>
          </li>
          <li class="header-nav__list">
            <a href="../index.html#acerca" class="header-nav__link">Acerca de</a>
          </li>
          <li class="header-nav__list">
            <a href="../index.html#preguntas" class="header-nav__link">Preguntas Frecuentes</a>
          </li>
          <li class="header-nav__list">
            <a href="../index.html#contacto" class="header-nav__link">Contacto</a>
          </li>
          <li class="header-nav__list">
            <a href="index.html" class="header-btn">Cotizar Servicio</a>
          </li>
        </nav>
      </div>
    </div>
  </header>

  <!-- Main Content Container -->
  <div class="quote-container">
    <h1>Solicitud de Cotización</h1>
    <p class="subtitle">Complete el formulario para recibir una cotización personalizada de nuestros servicios logísticos.</p>
    
    <form id="quoteForm">
      <!-- Información General -->
      <div class="form-section">
        <h2>Información General</h2>
        
        <label for="empresa">Empresa *</label>
        <input
          type="text"
          id="empresa"
          name="empresa"
          placeholder="Nombre de la empresa"
          required
        />
        <span class="error-message" id="error-empresa"></span>

        <div class="form-row">
          <div class="form-col">
            <label for="nombre">Nombre del Contacto *</label>
            <input
              type="text"
              id="nombre"
              name="nombre"
              placeholder="Nombre del contacto"
              required
            />
            <span class="error-message" id="error-nombre"></span>
          </div>
          
          <div class="form-col">
            <label for="correo">Correo Electrónico *</label>
            <input
              type="email"
              id="correo"
              name="correo"
              placeholder="Correo electrónico"
              required
            />
            <span class="error-message" id="error-correo"></span>
          </div>
        </div>
      </div>

      <!-- Información de Origen y Destino -->
      <div class="form-section">
        <h2>Origen y Destino</h2>
        
        <div class="form-row">
          <div class="form-col">
            <label for="direccion_origen">Dirección Origen *</label>
            <input
              type="text"
              id="direccion_origen"
              name="direccion_origen"
              placeholder="Dirección completa de origen"
              required
            />
            <span class="error-message" id="error-direccion_origen"></span>
          </div>
          
          <div class="form-col">
            <label for="cp_origen">CP Origen *</label>
            <input
              type="text"
              id="cp_origen"
              name="cp_origen"
              placeholder="Código postal"
              required
            />
            <span class="error-message" id="error-cp_origen"></span>
          </div>
        </div>

        <div class="form-row">
          <div class="form-col">
            <label for="direccion_destino">Dirección Destino *</label>
            <input
              type="text"
              id="direccion_destino"
              name="direccion_destino"
              placeholder="Dirección completa de destino"
              required
            />
            <span class="error-message" id="error-direccion_destino"></span>
          </div>
          
          <div class="form-col">
            <label for="cp_destino">CP Destino *</label>
            <input
              type="text"
              id="cp_destino"
              name="cp_destino"
              placeholder="Código postal"
              required
            />
            <span class="error-message" id="error-cp_destino"></span>
          </div>
        </div>
      </div>

      <!-- Información de la Carga -->
      <div class="form-section">
        <h2>Información de la Carga</h2>
        
        <label for="tipo_mercancia">Tipo de Mercancía *</label>
        <select id="tipo_mercancia" name="tipo_mercancia" required>
          <option value="">Seleccione el tipo de mercancía</option>
          <option value="sobredimensionado">Sobredimensionado</option>
          <option value="hazmat">Hazmat (Materiales Peligrosos)</option>
          <option value="perecedero">Perecedero</option>
          <option value="carga_general">Carga General</option>
        </select>
        <span class="error-message" id="error-tipo_mercancia"></span>
        <div class="form-row">

          <div class="form-col">
            <label for="piezas">Piezas *</label>
            <input
              type="number"
              id="piezas"
              name="piezas"
              placeholder="Número de piezas"
              min="1"
              required
            />
            <span class="error-message" id="error-piezas"></span>
          </div>
          
          <div class="form-col">
            <label for="peso">Peso (kg) *</label>
            <input
              type="number"
              id="peso"
              name="peso"
              placeholder="Peso total en kilogramos"
              min="0.1"
              step="0.1"
              required
            />
            <span class="error-message" id="error-peso"></span>
          </div>
        </div>

        <label for="dimensiones">Dimensiones *</label>
        <input
          type="text"
          id="dimensiones"
          name="dimensiones"
          placeholder="Largo x Ancho x Alto (cm)"
          required
        />
        <span class="error-message" id="error-dimensiones"></span>

        <label for="descripcion_mercancia">Descripción de la Mercancía *</label>
        <textarea
          id="descripcion_mercancia"
          name="descripcion_mercancia"
          placeholder="Describa detalladamente el tipo de mercancía a transportar"
          rows="4"
          required
        ></textarea>
        <span class="error-message" id="error-descripcion_mercancia"></span>
      </div>

      <!-- Disclaimer -->
      <div class="disclaimer-section" id="disclaimerSection" style="display: none;">
        <div class="disclaimer-box">
          <h3>Aviso Importante</h3>
          <p>Favor de considerar: En caso de haber alguna actualización de cualquier dato puede haber cambios en la cotización</p>
          <label class="checkbox-container">
            <input type="checkbox" id="acceptDisclaimer" />
            <span class="checkmark"></span>
            He leído y acepto el aviso anterior
          </label>
        </div>
      </div>

      <!-- Botones -->
      <div class="form-actions">
        <button type="button" id="showDisclaimer" class="btn-primary">
          Solicitar Cotización
        </button>
        <button type="submit" id="submitForm" class="btn-submit" style="display: none;">
          Enviar Solicitud
        </button>
      </div>
    </form>

    <!-- Mensaje de resultado -->
    <div id="result-message" class="result-message" style="display: none;"></div>
  </div>

  <a class="footer-link" href="https://stilogistica.mx">stilogistica.mx</a>

  <!-- Scripts -->
  <script defer src="../js/main.js"></script>
  <script src="script.js"></script>
</body>
</html>
