<?php
// Include configuration
require '../config.php';

// Set headers for JSON response and prevent caching
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Function to sanitize input data
function sanitize_input($data) {
    return htmlspecialchars(stripslashes(trim($data)));
}

// Function to validate input data with specific rules
function validate_quote_input($data, $type) {
    $data = sanitize_input($data);
    $error = null;

    switch ($type) {
        case 'empresa':
        case 'nombre':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (strlen($data) < 2) {
                $error = 'Debe tener al menos 2 caracteres.';
            } elseif (!preg_match('/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s\-\.\&,0-9]{2,}$/u', $data)) {
                $error = 'Formato inválido.';
            }
            break;

        case 'direccion_origen':
        case 'direccion_destino':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (strlen($data) < 10) {
                $error = 'La dirección debe ser más específica (mínimo 10 caracteres).';
            }
            break;

        case 'cp_origen':
        case 'cp_destino':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!preg_match('/^\d{5}$/', $data)) {
                $error = 'El código postal debe tener 5 dígitos.';
            }
            break;

        case 'piezas':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!is_numeric($data) || intval($data) < 1) {
                $error = 'Debe ser al menos 1 pieza.';
            }
            break;

        case 'peso':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!is_numeric($data) || floatval($data) <= 0) {
                $error = 'El peso debe ser mayor a 0.';
            }
            break;

        case 'dimensiones':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!preg_match('/^\d+\s*x\s*\d+\s*x\s*\d+/i', $data)) {
                $error = 'Formato: Largo x Ancho x Alto (ej: 100 x 50 x 30)';
            }
            break;

        case 'descripcion_mercancia':
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (strlen($data) < 10) {
                $error = 'La descripción debe tener al menos 10 caracteres.';
            }
            break;

        case 'tipo_mercancia':
            $valid_types = ['sobredimensionado', 'hazmat', 'perecedero', 'carga_general'];
            if (empty($data)) {
                $error = 'Debe seleccionar un tipo de mercancía.';
            } elseif (!in_array($data, $valid_types)) {
                $error = 'Tipo de mercancía inválido.';
            }
            break;
    }

    return ['value' => $data, 'error' => $error];
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $errors = [];
    $validated_data = [];

    // Define fields to validate
    $fields = [
        'empresa', 'nombre', 'direccion_origen', 'cp_origen',
        'direccion_destino', 'cp_destino', 'piezas', 'peso',
        'dimensiones', 'descripcion_mercancia', 'tipo_mercancia'
    ];

    // Validate each field
    foreach ($fields as $field) {
        $result = validate_quote_input(isset($_POST[$field]) ? $_POST[$field] : '', $field);
        $validated_data[$field] = $result['value'];
        if ($result['error']) {
            $errors[$field] = $result['error'];
        }
    }

    // If there are validation errors, return them
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Hay errores en el formulario.',
            'errors' => $errors
        ]);
        exit;
    }

    // Prepare email content
    $subject = 'Nueva Solicitud de Cotización - STI Logistics';
    
    // Create email body
    $email_body = "Nueva solicitud de cotización recibida:\n\n";
    $email_body .= "=== INFORMACIÓN DE LA EMPRESA ===\n";
    $email_body .= "Empresa: " . $validated_data['empresa'] . "\n";
    $email_body .= "Contacto: " . $validated_data['nombre'] . "\n\n";
    
    $email_body .= "=== ORIGEN Y DESTINO ===\n";
    $email_body .= "Dirección Origen: " . $validated_data['direccion_origen'] . "\n";
    $email_body .= "CP Origen: " . $validated_data['cp_origen'] . "\n";
    $email_body .= "Dirección Destino: " . $validated_data['direccion_destino'] . "\n";
    $email_body .= "CP Destino: " . $validated_data['cp_destino'] . "\n\n";
    
    $email_body .= "=== INFORMACIÓN DE LA CARGA ===\n";
    $email_body .= "Piezas: " . $validated_data['piezas'] . "\n";
    $email_body .= "Peso: " . $validated_data['peso'] . " kg\n";
    $email_body .= "Dimensiones: " . $validated_data['dimensiones'] . " cm\n";
    $email_body .= "Tipo de Mercancía: " . ucfirst(str_replace('_', ' ', $validated_data['tipo_mercancia'])) . "\n";
    $email_body .= "Descripción: " . $validated_data['descripcion_mercancia'] . "\n\n";
    
    $email_body .= "=== INFORMACIÓN ADICIONAL ===\n";
    $email_body .= "Fecha de solicitud: " . date('Y-m-d H:i:s') . "\n";
    $email_body .= "IP del cliente: " . $_SERVER['REMOTE_ADDR'] . "\n";

    // Email headers
    $headers = "From: " . EMAIL_FROM . "\r\n";
    $headers .= "Reply-To: " . EMAIL_FROM . "\r\n";
    $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

    // Multiple recipients - you can add more email addresses here
    $recipients = [
        EMAIL_TO, // Primary recipient from config
        CORREO_OPERADOR_1,
        CORREO_OPERADOR_2,
        CORREO_OPERADOR_3,
        CORREO_OPERADOR_4
    ];

    // Filter out empty recipients
    $recipients = array_filter($recipients, function($email) {
        return !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL);
    });

    $email_sent = false;
    $failed_recipients = [];

    // Send email to each recipient
    foreach ($recipients as $recipient) {
        if (filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
            if (mail($recipient, $subject, $email_body, $headers)) {
                $email_sent = true;
            } else {
                $failed_recipients[] = $recipient;
            }
        }
    }

    // Response based on email sending results
    if ($email_sent) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Solicitud de cotización enviada exitosamente.'
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Error al enviar la solicitud. Por favor, intente nuevamente.'
        ]);
    }

} else {
    // If not a POST request, reject
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método de solicitud no permitido.'
    ]);
}
?>
