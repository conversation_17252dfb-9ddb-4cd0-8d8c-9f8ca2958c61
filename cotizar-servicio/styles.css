/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 20px;
  font-family: 'Trebuchet MS', Arial, sans-serif;
  background-color: #f4f4f4;
  line-height: 1.6;
  color: #333;
}

/* Quote form container - renamed to avoid conflict with main site container */
.quote-container {
  max-width: 800px;
  margin: 40px auto 20px;
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Typography */
h1 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #000188;
  font-size: 2.2em;
  text-align: center;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 1.1em;
}

h2 {
  color: #000188;
  border-bottom: 2px solid #000188;
  padding-bottom: 5px;
  margin-top: 30px;
  margin-bottom: 20px;
  font-size: 1.4em;
}

h3 {
  color: #000188;
  margin-bottom: 10px;
}

/* Form Sections */
.form-section {
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-col {
  flex: 1;
}

/* Form Elements */
label {
  display: block;
  margin: 15px 0 5px;
  font-weight: bold;
  color: #333;
}

input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #000188;
  box-shadow: 0 0 5px rgba(0, 1, 136, 0.3);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

select {
  cursor: pointer;
}

/* Validation Styles */
.error-input {
  border-color: #dc3545 !important;
  box-shadow: 0 0 5px rgba(220, 53, 69, 0.3) !important;
}

.valid-input {
  border-color: #28a745 !important;
  box-shadow: 0 0 5px rgba(40, 167, 69, 0.3) !important;
}

.error-message {
  display: none;
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
  font-weight: normal;
}

.success-message {
  display: none;
  color: #28a745;
  font-size: 14px;
  margin-top: 5px;
  font-weight: normal;
}

/* Disclaimer Section */
.disclaimer-section {
  margin: 30px 0;
  animation: slideDown 0.3s ease-out;
}

.disclaimer-box {
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.disclaimer-box h3 {
  margin-top: 0;
  color: #856404;
}

.disclaimer-box p {
  margin-bottom: 15px;
  color: #856404;
  font-weight: 500;
}

/* Custom Checkbox */
.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal;
  margin: 0;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #856404;
  border-radius: 3px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: #856404;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  position: absolute;
  top: -2px;
  left: 3px;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

/* Buttons */
.form-actions {
  text-align: center;
  margin-top: 30px;
}

.btn-primary,
.btn-submit {
  padding: 12px 30px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 10px;
}

.btn-primary {
  background-color: #000188;
  color: white;
}

.btn-primary:hover {
  background-color: #000066;
  transform: translateY(-2px);
}

.btn-submit {
  background-color: #28a745;
  color: white;
}

.btn-submit:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.btn-primary:disabled,
.btn-submit:disabled,
.btn-disabled {
  background-color: #ccc !important;
  cursor: not-allowed !important;
  transform: none !important;
  opacity: 0.6;
}

.btn-primary:disabled:hover,
.btn-submit:disabled:hover,
.btn-disabled:hover {
  background-color: #ccc !important;
  transform: none !important;
}

/* Result Message */
.result-message {
  margin-top: 20px;
  padding: 15px;
  border-radius: 5px;
  text-align: center;
  font-weight: bold;
}

.result-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.result-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Footer Link */
.footer-link {
  display: block;
  text-align: center;
  margin-top: 20px;
  color: #999;
  text-decoration: none;
  font-size: 12px;
}

.footer-link:hover {
  color: #000188;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .quote-container {
    margin: 20px 10px 10px;
    padding: 20px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 0;
  }
  
  h1 {
    font-size: 1.8em;
  }
  
  .subtitle {
    font-size: 1em;
  }
  
  .btn-primary,
  .btn-submit {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}

@media (max-width: 480px) {
  body {
    padding: 10px;
  }
  
  .quote-container {
    padding: 15px;
  }
  
  h1 {
    font-size: 1.5em;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="email"],
  select,
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
